<template>
  <div class="json-form-editor">
    <el-form
      ref="fieldForm"
      :model="{ fields }"
      label-position="top"
      :rules="{}"
      :inline="false"
    >
      <div class="field-list" :style="{ marginLeft: indent + 'px' }">
        <!-- 顶级对象标题行 -->
        <div class="object-title-row" v-if="!nested">
          <el-row style="margin-bottom: 10px; padding: 8px 0">
            <el-col :span="10">
              <span class="object-name">cbm_agent</span>
              <el-tooltip
                popper-class="tooltip_popper"
                effect="light"
                content="智能体要完成任务，有时需要使用端侧的设备状态或工具执行结果。例如智
能体要控制设备，要预先了解设备的状态参数。为此，服务支持通过cbmagent
字段，给智能体输入AIUI服务外的参数。注意：
1.平台已预置device_status参数作为设备状态参数，便于模板智能体工作，该参
数名不可更改或被重复使用。
2.新增参数的解析与使用逻辑，请移步工作流智能体的“开始节点”定义。
3.对话体验仅给参数模拟一个虚假值，不会影响端侧调用，生产使用请自行开发
传参逻辑。"
                placement="bottom"
              >
                <i class="el-icon-warning object-type"></i>
              </el-tooltip>
            </el-col>
            <el-col :span="6" style="padding-left: 1px">
              <span style="visibility: hidden">占位</span>
            </el-col>
            <el-col :span="6" style="padding-left: 10px">
              <span style="visibility: hidden">占位</span>
            </el-col>
            <el-col :span="2" style="padding-left: 10px; text-align: center">
              <i
                class="el-icon-circle-plus-outline icon"
                @click="addField"
                title="添加参数"
              ></i>
            </el-col>
          </el-row>
        </div>

        <div class="param_header" v-if="!nested">
          <el-row style="margin-bottom: 15px">
            <el-col :span="10">参数名称</el-col>
            <el-col :span="6" style="padding-left: 1px">参数类型</el-col>
            <el-col :span="6" style="padding-left: 10px">取值</el-col>
            <el-col :span="2" style="padding-left: 10px; text-align: center">
              <span>操作</span>
            </el-col>
          </el-row>
        </div>

        <div v-for="(field, index) in fields" :key="index" class="field-item">
          <el-row :gutter="16" class="field-controls">
            <el-col :span="10">
              <!-- 字段名校验 -->
              <el-form-item
                :prop="'fields.' + index + '.name'"
                :rules="[
                  {
                    required: true,
                    message: '字段名不能为空',
                    trigger: 'blur',
                  },
                  {
                    pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
                    message:
                      '只能包含字母、数字或下划线，并且以字母或下划线开头',
                    trigger: 'blur',
                  },
                  {
                    validator: (rule, value, callback) =>
                      validateUniqueName(index, value, callback),
                    trigger: 'blur',
                  },
                ]"
              >
                <el-input v-model="field.name" placeholder="字段名"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-select
                v-model="field.type"
                placeholder="类型"
                @change="handleTypeChange(field)"
              >
                <el-option label="string" value="string"></el-option>
                <el-option label="number" value="number"></el-option>
                <el-option label="boolean" value="boolean"></el-option>
                <el-option label="object" value="object"></el-option>
                <el-option label="array" value="array"></el-option>
              </el-select>
            </el-col>

            <el-col :span="6">
              <template v-if="field.type === 'string'">
                <el-input v-model="field.value" placeholder="值"></el-input>
              </template>
              <template v-if="field.type === 'number'">
                <el-input-number v-model="field.value"></el-input-number>
              </template>
              <template v-if="field.type === 'boolean'">
                <el-switch v-model="field.value"></el-switch>
              </template>
            </el-col>

            <el-col :span="2">
              <i
                class="el-icon-remove-outline icon"
                @click="removeField(index)"
                v-if="fields.length > 1"
              ></i>
              <!-- 一级项的加号按钮已移到表头，只在嵌套组件中显示加号 -->
              <i
                class="el-icon-circle-plus-outline icon"
                @click="addField"
                v-if="nested && index === fields.length - 1"
              ></i>
            </el-col>
          </el-row>

          <!-- 嵌套对象 -->
          <div v-if="field.type === 'object'" class="nested-content">
            <json-form-editor
              nested
              :fields="field.children || []"
              :indent="indent + 20"
              @update="
                (updatedFields) => updateNestedFields(field, updatedFields)
              "
              ref="nestedEditors"
            />
          </div>

          <!-- 数组类型 -->
          <div v-if="field.type === 'array'" class="nested-content">
            <el-row :gutter="16" class="field-controls">
              <el-col :span="10">
                <el-input disabled value="[Array Item]"></el-input>
              </el-col>
              <el-col :span="6">
                <el-select
                  v-model="field.itemType"
                  placeholder="选择数组项类型"
                  @change="changeArrayItem(field)"
                >
                  <el-option label="string" value="string"></el-option>
                  <el-option label="number" value="number"></el-option>
                  <el-option label="boolean" value="boolean"></el-option>
                  <el-option label="object" value="object"></el-option>
                </el-select>
              </el-col>
              <el-col :span="6"></el-col>
              <el-col :span="2">
                <i
                  class="el-icon-circle-plus-outline icon"
                  @click="addArrayItem(field)"
                ></i>
              </el-col>
            </el-row>

            <div
              v-for="(item, itemIndex) in field.items"
              :key="itemIndex"
              class="array-item"
            >
              <el-row :gutter="16" class="array-item-controls">
                <el-col :span="22">
                  <template v-if="item.type === 'string'">
                    <el-input
                      v-model="item.value"
                      placeholder="字符串值"
                      style="width: 200px"
                    />
                  </template>
                  <template v-if="item.type === 'number'">
                    <el-input-number
                      v-model="item.value"
                      style="width: 200px"
                    />
                  </template>
                  <template v-if="item.type === 'boolean'">
                    <el-switch v-model="item.value" />
                  </template>
                  <template v-if="item.type === 'object'">
                    <json-form-editor
                      nested
                      :fields="item.children || []"
                      :indent="indent + 20"
                      @update="
                        (updatedFields) =>
                          updateArrayItemObject(field, itemIndex, updatedFields)
                      "
                      ref="nestedEditors"
                    />
                  </template>
                </el-col>
                <el-col :span="2">
                  <i
                    v-if="item.type !== 'object'"
                    class="el-icon-remove-outline icon"
                    @click="removeArrayItem(field, itemIndex)"
                    style="margin-left: 10px"
                  />
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'JsonFormEditor',
  props: {
    fields: {
      type: Array,
      default: () => [],
    },
    indent: {
      type: Number,
      default: 0,
    },
    nested: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    addField() {
      this.fields.push({
        name: '',
        type: 'string',
        value: '',
        children: [],
        items: [],
        itemType: 'string',
      })
      this.$emit('update', this.fields)
    },
    removeField(index) {
      this.fields.splice(index, 1)
      this.$emit('update', this.fields)
    },
    handleTypeChange(field) {
      switch (field.type) {
        case 'string':
          field.value = ''
          break
        case 'number':
          field.value = 0
          break
        case 'boolean':
          field.value = false
          break
        case 'object':
          field.children = [
            {
              name: '',
              type: 'string',
              value: '',
              children: [],
              items: [],
              itemType: 'string',
            },
          ]
          break
        case 'array':
          field.items = []
          field.itemType = 'string'
          break
      }
      this.$emit('update', this.fields)
    },
    updateNestedFields(field, updatedFields) {
      field.children = updatedFields
      this.$emit('update', this.fields)
    },
    addArrayItem(field) {
      const newItem = { type: field.itemType }
      switch (field.itemType) {
        case 'string':
          newItem.value = ''
          break
        case 'number':
          newItem.value = 0
          break
        case 'boolean':
          newItem.value = false
          break
        case 'object':
          newItem.children = [
            {
              name: '',
              type: 'string',
              value: '',
              children: [],
              items: [],
              itemType: 'string',
            },
          ]
          break
      }
      field.items.push(newItem)
      this.$emit('update', this.fields)
    },
    removeArrayItem(field, itemIndex) {
      field.items.splice(itemIndex, 1)
      this.$emit('update', this.fields)
    },
    changeArrayItem(field) {
      field.items = []
      this.addArrayItem(field)
    },
    updateArrayItemObject(field, itemIndex, updatedFields) {
      field.items[itemIndex].children = updatedFields
      this.$emit('update', this.fields)
    },

    validateUniqueName(currentIndex, value, callback) {
      const names = this.fields.map((f) => f.name?.trim()).filter(Boolean)
      const currentName = value?.trim()
      const duplicate = names.filter((name) => name === currentName)

      if (duplicate.length > 1) {
        callback(new Error('字段名重复，请修改'))
      } else {
        callback()
      }
    },

    // 暴露校验方法
    validate() {
      return new Promise((resolve) => {
        this.$refs.fieldForm.validate((valid) => {
          if (!valid) return resolve(false)

          // 校验嵌套编辑器
          const nestedEditors = this.$refs.nestedEditors || []
          const nestedList = Array.isArray(nestedEditors)
            ? nestedEditors
            : [nestedEditors]
          const nestedValidations = nestedList.map((editor) =>
            editor.validate()
          )
          Promise.all(nestedValidations).then((results) => {
            resolve(results.every((r) => r))
          })
        })
      })
    },
  },
}
</script>

<style scoped lang="scss">
.json-form-editor {
  // padding: 10px;
  margin-bottom: 0px;
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}
.field-controls {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.nested-content {
  padding-left: 20px;
}
.array-item {
  // padding: 5px 10px;
  // border-radius: 4px;
}
.array-item-controls {
  display: flex;
  align-items: center;
}
.icon {
  cursor: pointer;
  &:hover {
    color: #595959;
  }
}

.object-title-row {
  .object-name {
    font-weight: bold;
    color: #303133;
    font-size: 14px;
  }

  .object-type {
    color: #acacac;
    cursor: pointer;
    margin-left: 4px;
  }
  .tooltip_popper {
    max-width: 300px;
  }
}
</style>
